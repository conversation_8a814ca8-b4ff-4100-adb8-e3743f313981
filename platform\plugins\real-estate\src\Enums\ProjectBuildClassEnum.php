<?php

namespace Xmetr\RealEstate\Enums;

use Xmetr\Base\Facades\BaseHelper;
use Xmetr\Base\Supports\Enum;
use Illuminate\Support\HtmlString;

/**
 * @method static ProjectBuildClassEnum PREMIUM()
 * @method static ProjectBuildClassEnum BUSINESS()
 * @method static ProjectBuildClassEnum STANDARD()
 * @method static ProjectBuildClassEnum ECONOMY()
 */
class ProjectBuildClassEnum extends Enum
{
    public const PREMIUM = 'premium';

    public const BUSINESS = 'business';

    public const STANDARD = 'standard';

    public const ECONOMY = 'economy';

    public static $langPath = 'plugins/real-estate::project.build-classes';

    public function toHtml(): HtmlString|string|null
    {
        $color = match ($this->value) {
            self::PREMIUM => 'success',
            self::BUSINESS => 'info',
            self::STANDARD => 'warning',
            self::ECONOMY => 'danger',
            default => 'primary',
        };

        return BaseHelper::renderBadge($this->label(), $color);
    }
}
